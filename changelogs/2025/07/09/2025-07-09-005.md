# 2025-07-09 #5

## Commit: 2ad20d8
feat(auth): implement token encryption and improve auth security

add AES-256-GCM encryption for access/refresh tokens in database
update auth configuration with new encryption hooks
improve hooks.server.ts with better error handling and route protection
update .env.example with new encryption key requirements

Files changed:
- .env.example
- src/hooks.server.ts
- src/lib/client/auth.ts
- src/lib/server/auth.ts
- src/lib/server/encryption.ts


## Summary
Implementation of OAuth token encryption using AES-256-GCM and improvement of error handling and route protection in the authentication system.

## Changes

### [SECURITY] Implementation of OAuth token encryption
**What:** Added encryption for access and refresh tokens in the database using AES-256-GCM.
**Why:** OAuth tokens contain sensitive data that should not be stored as plaintext in the database, even if the database itself is secured.
**How:** Created an `encryption.ts` module with `encrypt` and `decrypt` functions that use Node.js `crypto` for encryption/decryption with AES-256-GCM. Integrated these functions with Better Auth through database hooks.

```typescript
// src/lib/server/encryption.ts (fragment)
export function encrypt(text: string): string {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
  const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
  const tag = cipher.getAuthTag();
  return Buffer.concat([iv, tag, encrypted]).toString('base64');
}
```

### [SECURITY] Improved error handling in hooks.server.ts
**What:** Enhanced error handling in hooks.server.ts and added more precise route protection.
**Why:** The previous implementation could cause unexpected errors in case of session problems, and route protection was too general.
**How:** Added exception handling when retrieving sessions and refined the redirection logic for protected routes.

```typescript
// src/hooks.server.ts (fragment)
try {
  const sessionData = await auth.api.getSession({ headers: event.request.headers });
  event.locals.session = sessionData?.session;
  event.locals.user = sessionData?.user;
} catch (err) {
  console.error('Błąd pobierania sesji w hooks.server.ts:', err);
  event.locals.session = undefined;
  event.locals.user = undefined;
}
```

## Notes

### Security Requirements
- Added a new environment variable `TOKEN_ENCRYPTION_KEY` (64 hex characters = 32 bytes) for token encryption
- Implemented key validation at application startup
- Used AES-256-GCM, which provides both confidentiality and data integrity

### Encrypted Data Structure
- Format: `[IV (12 bytes)][AuthTag (16 bytes)][Encrypted data]` encoded in Base64
- IV (initialization vector) is randomly generated for each token
- AuthTag ensures data integrity and authenticity

### Error Handling
- The `decrypt` function returns `undefined` in case of decryption error
- Added exception handling in hooks.server.ts to prevent session problems from causing application crashes

### Next Steps
- Implementation of actual email sending (currently only console logging)
- Consider adding encryption key rotation
- Add monitoring of failed decryption attempts (potential attack attempts)
