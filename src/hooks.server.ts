import { redirect, type Handle } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { svelteKitHandler } from 'better-auth/svelte-kit';
import { auth } from '$lib/server/auth';

/**
 * Handler dla endpointów Better Auth (np. /api/auth/*).
 * Obsługuje logowanie, wylogowywanie, callbacki OAuth itp.
 * Musi być pierwszy w sekwencji.
 */
const betterAuthHook: Handle = ({ event, resolve }) => {
	return svelteKitHandler({ event, resolve, auth });
};

/**
 * Główny hook aplikacyjny:
 * 1. Pobiera sesję użytkownika na początku każdego żądania.
 * 2. Wstawia dane sesji i użytkownika do `event.locals`.
 * 3. Chroni trasy w oparciu o grupy folderów.
 */
const appHook: Handle = async ({ event, resolve }) => {
	// 1. <PERSON><PERSON><PERSON> i wstaw do `locals`.
	// <PERSON><PERSON>y to dla każdego żądania, aby mieć spójny dostęp w całej aplikacji.
	try {
		const sessionData = await auth.api.getSession({ headers: event.request.headers });
		event.locals.session = sessionData?.session;
		event.locals.user = sessionData?.user;
	} catch (err) {
		// W przypadku błędu biblioteki auth, logujemy go, ale nie przerywamy działania aplikacji.
		// Użytkownik będzie traktowany jako niezalogowany.
		console.error('Błąd pobierania sesji w hooks.server.ts:', err);
		event.locals.session = undefined;
		event.locals.user = undefined;
	}

	// 2. Chroń trasy.
	const routeId = event.route.id;

	// Strefa chroniona (`/(protected)`) wymaga zalogowanego użytkownika.
	if (routeId?.startsWith('/(protected)') && !event.locals.user) {
		return redirect(303, '/login');
	}

	// Strefa dla gości (`/(auth)`) jest dostępna tylko dla niezalogowanych.
	if (routeId?.startsWith('/(auth)') && event.locals.user) {
		return redirect(303, '/dashboard');
	}

	// Jeśli wszystko jest w porządku, kontynuuj przetwarzanie żądania.
	return resolve(event);
};

// Łączymy hooki w sekwencję. `betterAuthHook` musi być pierwszy.
export const handle = sequence(betterAuthHook, appHook);