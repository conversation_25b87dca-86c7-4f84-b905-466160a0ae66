import crypto from 'crypto';
import { TOKEN_ENCRYPTION_KEY } from '$env/static/private';

if (!TOKEN_ENCRYPTION_KEY || TOKEN_ENCRYPTION_KEY.length !== 64) {
	throw new Error('TOKEN_ENCRYPTION_KEY must be a 64-character hex string (32 bytes).');
}

const ALGORITHM = 'aes-256-gcm';
const KEY = Buffer.from(TOKEN_ENCRYPTION_KEY, 'hex');
const IV_LENGTH = 12;
const AUTH_TAG_LENGTH = 16;

/**
 * Encrypts text using AES-256-GCM.
 * Output format (Base64): [IV][AuthTag][EncryptedData]
 */
export function encrypt(text: string): string {
	const iv = crypto.randomBytes(IV_LENGTH);
	const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
	const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
	const tag = cipher.getAuthTag();
	return Buffer.concat([iv, tag, encrypted]).toString('base64');
}

/**
 * Decrypts data encrypted by the encrypt function.
 * Returns undefined if decryption fails (e.g., wrong key or corrupted data).
 */
export function decrypt(data: string): string | undefined {
	try {
		const buf = Buffer.from(data, 'base64');
		if (buf.length < IV_LENGTH + AUTH_TAG_LENGTH) {
			throw new Error('Invalid encrypted data format: too short.');
		}
		const iv = buf.subarray(0, IV_LENGTH);
		const tag = buf.subarray(IV_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH);
		const encrypted = buf.subarray(IV_LENGTH + AUTH_TAG_LENGTH);
		const decipher = crypto.createDecipheriv(ALGORITHM, KEY, iv);
		decipher.setAuthTag(tag);
		const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
		return decrypted.toString('utf8');
	} catch (error) {
		console.error('Decryption error:', error);
		return undefined;
	}
}