import { createAuthClient } from 'better-auth/svelte';
import { PUBLIC_AUTH_URL } from '$env/static/public';

/**
 * Centralna instancja klienta Better Auth dla aplikacji.
 * Konfiguruje połączenie z serwerem auth i opcjonalne pluginy klienckie.
 */
export const authClient = createAuthClient({
  baseURL: PUBLIC_AUTH_URL ?? 'http://localhost:5173' // Fallback dla dev
});

// Eksport kluczowych metod dla wygody w komponentach
export const { signIn, signOut, signUp, useSession } = authClient;