<script lang="ts">
	import AuthForm from '$lib/components/auth/AuthForm.svelte';
	let { data } = $props();
</script>

<div class="flex h-screen items-center justify-center">
	<AuthForm
		form={data.form}
		actionName="Sign In"
		submitText="Sign In"
		enableGoogle
		fields={[
			{ name: 'email', label: 'Email', type: 'email', placeholder: '<EMAIL>' },
			{ name: 'password', label: 'Password', type: 'password' }
		]}
		links={[
			{ href: '/register', text: "Don't have an account?" },
			{ href: '/forgot-password', text: 'Forgot password?' }
		]}
	/>
</div>