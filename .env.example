# PostgreSQL connection
DATABASE_URL="postgres://root:mysecretpassword@localhost:5432/local"

# URL klienta aplik<PERSON> (SvelteKit)
PUBLIC_AUTH_URL="http://localhost:5173"

# Better Auth
# Wygeneruj losowy secret o długości co najmniej 32 bajtów (np. 64 znaki hex):
#   openssl rand -hex 64
BETTER_AUTH_SECRET="your-hex-secret-64-chars"

BETTER_AUTH_URL="http://localhost:5173"

# Klucz AES-256-GCM do szyfrowania accessToken/refreshToken
# Wymaga 32 bajtów = 64 znaki hex:
#   openssl rand -hex 32
TOKEN_ENCRYPTION_KEY="your-hex-key-64-chars"

# Google OAuth (lub inne social providers)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"