<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import { LogIn } from 'lucide-svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { toast } from 'svelte-sonner';
	import type { z } from 'zod/v4';
	import type { SuperValidated, FormResult } from 'sveltekit-superforms';

	type Field = {
		name: string;
		label: string;
		type?: 'text' | 'email' | 'password';
		placeholder?: string;
	};

	type Props = {
		form: SuperValidated<Record<string, unknown>>;
		formId?: string;
		actionName: string; // Np. 'Sign In' lub 'Sign Up'
		submitText: string;
		fields: Field[];
		enableGoogle?: boolean;
		links?: { href: string; text: string }[];
	};

	let { form: initialForm, formId, actionName, submitText, fields, enableGoogle = false, links = [] } = $props<Props>();

	const { form, errors, submitting, enhance, message } = superForm(initialForm, {
		validators: zodClient(initialForm.constraints),
		dataType: 'json',
		onResult: ({ result }: { result: FormResult<Record<string, any>> }) => {
			if (result.type === 'success' && result.data?.message) {
				toast.success(result.data.message);
			} else if (result.type === 'error') {
				toast.error(result.error.message);
			} else if (result.type === 'failure' && result.data?.message) {
				toast.error(result.data.message);
			}
		},
		id: formId // Ważne dla wielu formularzy na jednej stronie
	});
</script>

<Card.Root class="mx-auto w-full max-w-sm">
	<Card.Header class="text-center">
		<Card.Title class="text-2xl">{actionName}</Card.Title>
	</Card.Header>
	<Card.Content class="flex flex-col gap-4">
		{#if enableGoogle}
			<form method="POST" action="?/google" use:enhance>
				<Button variant="outline" class="w-full">
					<LogIn class="mr-2 h-4 w-4" />
					Continue with Google
				</Button>
			</form>
			<div class="relative flex items-center py-2">
				<div class="flex-grow border-t"></div>
				<span class="mx-4 flex-shrink text-xs uppercase text-muted-foreground">Or continue with</span>
				<div class="flex-grow border-t"></div>
			</div>
		{/if}

		<form method="POST" use:enhance class="flex flex-col gap-4">
			{#if $errors._errors}
				<div class="text-sm font-medium text-destructive">{$errors._errors.join(', ')}</div>
			{/if}

			{#each fields as field}
				<div class="space-y-2">
					<label for={field.name} class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
						{field.label}
					</label>
					<Input 
						id={field.name}
						name={field.name}
						type={field.type ?? 'text'} 
						bind:value={$form[field.name]} 
						placeholder={field.placeholder}
						class={$errors[field.name] ? 'border-destructive' : ''}
					/>
					{#if $errors[field.name]}
						<p class="text-sm font-medium text-destructive">{$errors[field.name]}</p>
					{/if}
				</div>
			{/each}

			<div class="flex items-center justify-between text-sm">
				{#each links as link}
					<a href={link.href} class="underline hover:text-primary">
						{link.text}
					</a>
				{/each}
			</div>

			<Button type="submit" class="w-full" disabled={$submitting}>
				{$submitting ? 'Submitting...' : submitText}
			</Button>
		</form>
	</Card.Content>
</Card.Root>