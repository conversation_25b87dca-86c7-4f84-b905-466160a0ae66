import { superValidate, message } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail, redirect } from '@sveltejs/kit';
import { auth } from '$lib/server/auth';
import { SignInSchema } from '$lib/shared/schemas';
import { APIError } from 'better-auth/api';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	if (locals.user) redirect(303, '/dashboard');
	const form = await superValidate(zod(SignInSchema));
	return { form };
};

export const actions: Actions = {
	default: async (event) => {
		const form = await superValidate(event, zod(SignInSchema));
		if (!form.valid) return fail(400, { form });

		try {
			await auth.api.signInEmail({
				body: form.data as { email: string; password: string },
				headers: event.request.headers
			});
		} catch (e) {
			if (e instanceof APIError) {
				return message(form, e.message, { status: 400 });
			}
			return message(form, 'An unexpected error occurred.', { status: 500 });
		}
		
		redirect(303, '/dashboard');
	},
	google: async () => {
		const res = await auth.api.signInSocial({
			body: { provider: 'google', callbackURL: '/dashboard' }
		});
		redirect(302, res.url!);
	}
};