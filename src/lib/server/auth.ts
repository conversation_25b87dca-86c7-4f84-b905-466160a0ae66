import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { db } from '$lib/server/db';
import * as schema from '$lib/server/db/schema';
import type { Account, NewAccount } from '$lib/server/db/schema';
import {
	BETTER_AUTH_SECRET,
	BETTER_AUTH_URL,
	GOOGLE_CLIENT_ID,
	GOOGLE_CLIENT_SECRET,
	TOKEN_ENCRYPTION_KEY
} from '$env/static/private';
import { encrypt, decrypt } from './encryption';

if (!BETTER_AUTH_SECRET || !BETTER_AUTH_URL) {
	throw new Error('BETTER_AUTH_SECRET or BETTER_AUTH_URL is not set');
}
if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
	throw new Error('GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET is not set for Google OAuth');
}
if (!TOKEN_ENCRYPTION_KEY || TOKEN_ENCRYPTION_KEY.length !== 64) {
	throw new Error('TOKEN_ENCRYPTION_KEY must be a 64-character hex string (32 bytes).');
}

export const auth = betterAuth({
	secret: BETTER_AUTH_SECRET,
	appName: 'SvelteKit App',
	database: drizzleAdapter(db, { provider: 'pg', schema }),

	databaseHooks: {
		account: {
			create: {
				async before(account: NewAccount) {
					const acc = { ...account };
					if (acc.accessToken) acc.accessToken = encrypt(acc.accessToken);
					if (acc.refreshToken) acc.refreshToken = encrypt(acc.refreshToken);
					return { data: acc };
				}
			},
			update: {
				async before(account: Partial<Account>) {
					const acc = { ...account };
					if (acc.accessToken) acc.accessToken = encrypt(acc.accessToken);
					if (acc.refreshToken) acc.refreshToken = encrypt(acc.refreshToken);
					return { data: acc };
				}
			},
			read: {
				async after(account: Account) {
					const acc = { ...account };
					if (acc.accessToken) {
						const decrypted = decrypt(acc.accessToken);
						acc.accessToken = decrypted ?? acc.accessToken;
					}
					if (acc.refreshToken) {
						const decrypted = decrypt(acc.refreshToken);
						acc.refreshToken = decrypted ?? acc.refreshToken;
					}
					return { data: acc };
				}
			}
		}
	},

	emailAndPassword: {
		enabled: true,
		requireEmailVerification: true,
		autoSignIn: true,
		sendResetPassword: async ({ user, url }) => {
			console.log(`TODO: Send reset password email to ${user.email} with URL ${url}`);
		}
	},
	socialProviders: {
		google: {
			clientId: GOOGLE_CLIENT_ID,
			clientSecret: GOOGLE_CLIENT_SECRET,
			redirectURI: `${BETTER_AUTH_URL}/api/auth/callback/google`
		}
	},
	emailVerification: {
		sendVerificationEmail: async ({ user, url }) => {
			console.log(`TODO: Send verification email to ${user.email} with URL ${url}`);
		}
	},
	session: {
		cookieCache: { enabled: true, maxAge: 5 * 60 }
	},
	rateLimit: { storage: 'database' },
	trustedOrigins: (request) => {
		if (request.url.startsWith('http://localhost:')) {
			return ['http://localhost:5173'];
		}
		return [new URL(BETTER_AUTH_URL).origin];
	}
});

export type Auth = typeof auth;