import { z } from 'zod';

// Schemat logowania
export const SignInSchema = z.object({
  email: z.string().email('Invalid email address.'),
  password: z.string().min(8, 'Password must be at least 8 characters long.')
});
export type SignIn = z.infer<typeof SignInSchema>;

// Schemat rejestracji
export const SignUpSchema = SignInSchema.extend({
  name: z.string().min(2, 'Name must be at least 2 characters long.').optional(), // Optional for Better Auth
  passwordConfirmation: z.string()
}).refine((data) => data.password === data.passwordConfirmation, {
  message: 'Passwords do not match.',
  path: ['passwordConfirmation']
});
export type SignUp = z.infer<typeof SignUpSchema>;

// Schemat odpowiedzi akcji formularza
export type FormActionResult<T> = {
  status: 'success';
  data: T;
} | {
  status: 'error';
  error: string;
  fields?: Partial<T>; // Form fields in case of error
  errors?: Record<string, string>; // Detailed validation errors
};